<?php

namespace App\Filament\Resources\TimeEntries\Pages;

use App\Filament\Resources\TimeEntries\TimeEntryResource;
use App\Models\TimeEntry;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Auth;

class ListTimeEntries extends ListRecords
{
    protected static string $resource = TimeEntryResource::class;

    protected function getHeaderActions(): array
    {
        $todayEntry = Auth::user()->todayTimeEntry();

        return [
            Action::make('clock_in')
                ->label('Bater Ponto - Entrada')
                ->icon('heroicon-o-clock')
                ->color('success')
                ->visible(fn () => !$todayEntry || !$todayEntry->entry_time)
                ->action(function () {
                    $this->clockIn();
                }),

            Action::make('lunch_start')
                ->label('Início do Almoço')
                ->icon('heroicon-o-pause')
                ->color('warning')
                ->visible(fn () => $todayEntry && $todayEntry->entry_time && !$todayEntry->lunch_start_time)
                ->action(function () {
                    $this->lunchStart();
                }),

            Action::make('lunch_end')
                ->label('Fim do Almoço')
                ->icon('heroicon-o-play')
                ->color('info')
                ->visible(fn () => $todayEntry && $todayEntry->lunch_start_time && !$todayEntry->lunch_end_time)
                ->action(function () {
                    $this->lunchEnd();
                }),

            Action::make('clock_out')
                ->label('Bater Ponto - Saída')
                ->icon('heroicon-o-clock')
                ->color('danger')
                ->visible(fn () => $todayEntry && $todayEntry->entry_time && !$todayEntry->exit_time)
                ->action(function () {
                    $this->clockOut();
                }),

            CreateAction::make()
                ->label('Registro Manual'),
        ];
    }

    protected function clockIn(): void
    {
        $user = Auth::user();
        $today = today();

        $timeEntry = TimeEntry::firstOrCreate(
            [
                'user_id' => $user->id,
                'date' => $today,
            ],
            [
                'entry_time' => Carbon::now()->format('H:i:s'),
                'is_manual_entry' => false,
            ]
        );

        if ($timeEntry->wasRecentlyCreated || !$timeEntry->entry_time) {
            $timeEntry->update([
                'entry_time' => Carbon::now()->format('H:i:s'),
                'is_manual_entry' => false,
            ]);

            Notification::make()
                ->title('Entrada registrada!')
                ->body('Entrada registrada às ' . Carbon::now()->format('H:i'))
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Entrada já registrada')
                ->body('Você já registrou entrada hoje às ' . Carbon::parse($timeEntry->entry_time)->format('H:i'))
                ->warning()
                ->send();
        }
    }

    protected function lunchStart(): void
    {
        $user = Auth::user();
        $todayEntry = $user->todayTimeEntry();

        if ($todayEntry) {
            $todayEntry->update([
                'lunch_start_time' => Carbon::now()->format('H:i:s'),
            ]);

            Notification::make()
                ->title('Início do almoço registrado!')
                ->body('Almoço iniciado às ' . Carbon::now()->format('H:i'))
                ->success()
                ->send();
        }
    }

    protected function lunchEnd(): void
    {
        $user = Auth::user();
        $todayEntry = $user->todayTimeEntry();

        if ($todayEntry) {
            $todayEntry->update([
                'lunch_end_time' => Carbon::now()->format('H:i:s'),
            ]);

            Notification::make()
                ->title('Fim do almoço registrado!')
                ->body('Almoço finalizado às ' . Carbon::now()->format('H:i'))
                ->success()
                ->send();
        }
    }

    protected function clockOut(): void
    {
        $user = Auth::user();
        $todayEntry = $user->todayTimeEntry();

        if ($todayEntry) {
            $todayEntry->update([
                'exit_time' => Carbon::now()->format('H:i:s'),
            ]);

            $workedMinutes = $todayEntry->getTotalWorkedMinutes();
            $overtimeMinutes = $todayEntry->getOvertimeMinutes();
            $missingMinutes = $todayEntry->getMissingMinutes();

            $message = 'Saída registrada às ' . Carbon::now()->format('H:i') . "\n";
            $message .= 'Tempo trabalhado: ' . TimeEntry::formatMinutes($workedMinutes);

            if ($overtimeMinutes > 0) {
                $message .= "\nHoras extras: +" . TimeEntry::formatMinutes($overtimeMinutes);
            } elseif ($missingMinutes > 0) {
                $message .= "\nHoras devendo: -" . TimeEntry::formatMinutes($missingMinutes);
            }

            Notification::make()
                ->title('Saída registrada!')
                ->body($message)
                ->success()
                ->send();
        }
    }
}
