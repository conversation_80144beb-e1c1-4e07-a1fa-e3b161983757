<?php

namespace App\Filament\Resources\TimeEntries\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Auth;

class TimeEntryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Informações Básicas')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('user_id')
                                    ->label('Usuário')
                                    ->relationship('user', 'name')
                                    ->default(Auth::id())
                                    ->required()
                                    ->disabled(fn () => !Auth::user()->can('manage_all_time_entries')),
                                DatePicker::make('date')
                                    ->label('Data')
                                    ->default(today())
                                    ->required(),
                            ]),
                    ]),

                Section::make('Horários')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TimePicker::make('entry_time')
                                    ->label('Entrada')
                                    ->seconds(false),
                                TimePicker::make('exit_time')
                                    ->label('Saída')
                                    ->seconds(false),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TimePicker::make('lunch_start_time')
                                    ->label('Início do Almoço')
                                    ->seconds(false),
                                TimePicker::make('lunch_end_time')
                                    ->label('Fim do Almoço')
                                    ->seconds(false),
                            ]),
                    ]),

                Section::make('Observações')
                    ->schema([
                        Textarea::make('notes')
                            ->label('Observações')
                            ->rows(3)
                            ->columnSpanFull(),
                        Toggle::make('is_manual_entry')
                            ->label('Entrada Manual')
                            ->helperText('Marque se este registro foi inserido manualmente')
                            ->default(true),
                    ]),
            ]);
    }
}
