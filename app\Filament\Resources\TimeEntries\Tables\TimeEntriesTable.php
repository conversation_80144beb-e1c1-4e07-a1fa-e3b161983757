<?php

namespace App\Filament\Resources\TimeEntries\Tables;

use App\Models\TimeEntry;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;

class TimeEntriesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->label('Usuário')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: !Auth::user()?->can('manage_all_time_entries')),
                TextColumn::make('date')
                    ->label('Data')
                    ->date('d/m/Y')
                    ->sortable(),
                TextColumn::make('entry_time')
                    ->label('Entrada')
                    ->time('H:i')
                    ->sortable(),
                TextColumn::make('lunch_start_time')
                    ->label('Início Almoço')
                    ->time('H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('lunch_end_time')
                    ->label('Fim Almoço')
                    ->time('H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('exit_time')
                    ->label('Saída')
                    ->time('H:i')
                    ->sortable(),
                TextColumn::make('total_worked')
                    ->label('Horas Trabalhadas')
                    ->getStateUsing(function (TimeEntry $record): string {
                        $minutes = $record->getTotalWorkedMinutes();
                        return TimeEntry::formatMinutes($minutes);
                    })
                    ->sortable(false),
                TextColumn::make('overtime')
                    ->label('Horas Extras')
                    ->getStateUsing(function (TimeEntry $record): string {
                        $minutes = $record->getOvertimeMinutes();
                        return $minutes > 0 ? '+' . TimeEntry::formatMinutes($minutes) : '-';
                    })
                    ->color(fn (TimeEntry $record): string => $record->getOvertimeMinutes() > 0 ? 'success' : 'gray')
                    ->sortable(false),
                TextColumn::make('missing')
                    ->label('Horas Devendo')
                    ->getStateUsing(function (TimeEntry $record): string {
                        $minutes = $record->getMissingMinutes();
                        return $minutes > 0 ? '-' . TimeEntry::formatMinutes($minutes) : '-';
                    })
                    ->color(fn (TimeEntry $record): string => $record->getMissingMinutes() > 0 ? 'danger' : 'gray')
                    ->sortable(false),
                IconColumn::make('is_manual_entry')
                    ->label('Manual')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('created_at')
                    ->label('Criado em')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('user_id')
                    ->label('Usuário')
                    ->relationship('user', 'name')
                    ->visible(fn () => Auth::user()?->can('manage_all_time_entries')),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }
}
