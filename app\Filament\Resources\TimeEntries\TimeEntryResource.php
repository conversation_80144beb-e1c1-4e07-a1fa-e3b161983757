<?php

namespace App\Filament\Resources\TimeEntries;

use App\Filament\Resources\TimeEntries\Pages\CreateTimeEntry;
use App\Filament\Resources\TimeEntries\Pages\EditTimeEntry;
use App\Filament\Resources\TimeEntries\Pages\ListTimeEntries;
use App\Filament\Resources\TimeEntries\Schemas\TimeEntryForm;
use App\Filament\Resources\TimeEntries\Tables\TimeEntriesTable;
use App\Models\TimeEntry;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class TimeEntryResource extends Resource
{
    protected static ?string $model = TimeEntry::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedClock;

    protected static ?string $navigationLabel = 'Controle de Ponto';

    protected static ?string $modelLabel = 'Registro de Ponto';

    protected static ?string $pluralModelLabel = 'Registros de Ponto';

    protected static ?int $navigationSort = 1;

    public static function form(Schema $schema): Schema
    {
        return TimeEntryForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return TimeEntriesTable::configure($table);
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Se o usuário não pode gerenciar todos os registros, mostrar apenas os seus
        if (!Auth::user()?->can('manage_all_time_entries')) {
            $query->where('user_id', Auth::id());
        }

        return $query;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTimeEntries::route('/'),
            'create' => CreateTimeEntry::route('/create'),
            'edit' => EditTimeEntry::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return true;
    }

    public static function canEdit($record): bool
    {
        // Usuários só podem editar seus próprios registros, a menos que tenham permissão especial
        if (Auth::user()?->can('manage_all_time_entries')) {
            return true;
        }

        return $record->user_id === Auth::id();
    }

    public static function canDelete($record): bool
    {
        // Usuários só podem deletar seus próprios registros, a menos que tenham permissão especial
        if (Auth::user()?->can('manage_all_time_entries')) {
            return true;
        }

        return $record->user_id === Auth::id();
    }
}
