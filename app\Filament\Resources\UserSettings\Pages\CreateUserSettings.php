<?php

namespace App\Filament\Resources\UserSettings\Pages;

use App\Filament\Resources\UserSettings\UserSettingsResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateUserSettings extends CreateRecord
{
    protected static string $resource = UserSettingsResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Se o usuário não pode gerenciar todas as configurações, definir automaticamente o user_id
        if (!Auth::user()?->can('manage_all_settings')) {
            $data['user_id'] = Auth::id();
        }

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return UserSettingsResource::getUrl('index');
    }
}
