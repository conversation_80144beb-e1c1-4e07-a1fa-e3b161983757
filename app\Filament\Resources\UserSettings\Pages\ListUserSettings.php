<?php

namespace App\Filament\Resources\UserSettings\Pages;

use App\Filament\Resources\UserSettings\UserSettingsResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Auth;

class ListUserSettings extends ListRecords
{
    protected static string $resource = UserSettingsResource::class;

    public function mount(): void
    {
        parent::mount();

        // Se o usuário não pode gerenciar todas as configurações e já tem configurações,
        // redirecionar para a página de edição
        if (!Auth::user()?->can('manage_all_settings')) {
            $userSettings = Auth::user()->settings;
            if ($userSettings) {
                $this->redirect(UserSettingsResource::getUrl('edit', ['record' => $userSettings]));
            }
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Criar Configurações')
                ->visible(fn () => UserSettingsResource::canCreate()),
        ];
    }
}
