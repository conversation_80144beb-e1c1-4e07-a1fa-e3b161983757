<?php

namespace App\Filament\Resources\UserSettings\Schemas;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Auth;

class UserSettingsForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Usuário')
                    ->schema([
                        Select::make('user_id')
                            ->label('Usuário')
                            ->relationship('user', 'name')
                            ->default(Auth::id())
                            ->required()
                            ->disabled(fn () => !Auth::user()?->can('manage_all_settings')),
                    ])
                    ->visible(fn () => Auth::user()?->can('manage_all_settings')),

                Section::make('Carga Horária')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('daily_work_hours')
                                    ->label('Horas de Trabalho por Dia')
                                    ->required()
                                    ->numeric()
                                    ->default(8)
                                    ->minValue(1)
                                    ->maxValue(12)
                                    ->suffix('horas'),
                                TextInput::make('daily_work_minutes')
                                    ->label('Minutos Adicionais')
                                    ->required()
                                    ->numeric()
                                    ->default(0)
                                    ->minValue(0)
                                    ->maxValue(59)
                                    ->suffix('minutos'),
                            ]),
                    ]),

                Section::make('Horários Padrão')
                    ->schema([
                        Grid::make(1)
                            ->schema([
                                TimePicker::make('default_start_time')
                                    ->label('Horário Padrão de Entrada')
                                    ->default('08:00:00')
                                    ->required()
                                    ->seconds(false),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TimePicker::make('lunch_start_time')
                                    ->label('Início do Almoço')
                                    ->default('12:00:00')
                                    ->required()
                                    ->seconds(false),
                                TimePicker::make('lunch_end_time')
                                    ->label('Fim do Almoço')
                                    ->default('13:00:00')
                                    ->required()
                                    ->seconds(false),
                            ]),
                        Toggle::make('auto_lunch_break')
                            ->label('Considerar Pausa para Almoço Automaticamente')
                            ->helperText('Se ativado, o tempo de almoço será automaticamente descontado do cálculo de horas trabalhadas')
                            ->default(true),
                    ]),
            ]);
    }
}
