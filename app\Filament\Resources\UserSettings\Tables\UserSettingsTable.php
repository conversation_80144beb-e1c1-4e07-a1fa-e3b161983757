<?php

namespace App\Filament\Resources\UserSettings\Tables;

use App\Models\UserSettings;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;

class UserSettingsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->label('Usuário')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: !Auth::user()?->can('manage_all_settings')),
                TextColumn::make('daily_work_formatted')
                    ->label('Carga Horária Diária')
                    ->getStateUsing(function (UserSettings $record): string {
                        return $record->getDailyWorkFormatted();
                    })
                    ->sortable(false),
                TextColumn::make('default_start_time')
                    ->label('Entrada Padrão')
                    ->time('H:i')
                    ->sortable(),
                TextColumn::make('lunch_start_time')
                    ->label('Início Almoço')
                    ->time('H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('lunch_end_time')
                    ->label('Fim Almoço')
                    ->time('H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('lunch_duration')
                    ->label('Duração Almoço')
                    ->getStateUsing(function (UserSettings $record): string {
                        if ($record->lunch_start_time && $record->lunch_end_time) {
                            $start = \Carbon\Carbon::parse($record->lunch_start_time);
                            $end = \Carbon\Carbon::parse($record->lunch_end_time);
                            $minutes = $end->diffInMinutes($start);
                            return \App\Models\TimeEntry::formatMinutes($minutes);
                        }
                        return '-';
                    })
                    ->sortable(false),
                IconColumn::make('auto_lunch_break')
                    ->label('Almoço Automático')
                    ->boolean(),
                TextColumn::make('created_at')
                    ->label('Criado em')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('Atualizado em')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
