<?php

namespace App\Filament\Resources\UserSettings;

use App\Filament\Resources\UserSettings\Pages\CreateUserSettings;
use App\Filament\Resources\UserSettings\Pages\EditUserSettings;
use App\Filament\Resources\UserSettings\Pages\ListUserSettings;
use App\Filament\Resources\UserSettings\Schemas\UserSettingsForm;
use App\Filament\Resources\UserSettings\Tables\UserSettingsTable;
use App\Models\UserSettings;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class UserSettingsResource extends Resource
{
    protected static ?string $model = UserSettings::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedCog6Tooth;

    protected static ?string $navigationLabel = 'Configurações';

    protected static ?string $modelLabel = 'Configuração';

    protected static ?string $pluralModelLabel = 'Configurações';

    protected static ?int $navigationSort = 2;

    public static function form(Schema $schema): Schema
    {
        return UserSettingsForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return UserSettingsTable::configure($table);
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Se o usuário não pode gerenciar todas as configurações, mostrar apenas as suas
        if (!Auth::user()?->can('manage_all_settings')) {
            $query->where('user_id', Auth::id());
        }

        return $query;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListUserSettings::route('/'),
            'create' => CreateUserSettings::route('/create'),
            'edit' => EditUserSettings::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        // Verificar se o usuário já tem configurações
        $userSettings = Auth::user()?->settings;
        return !$userSettings;
    }

    public static function canEdit($record): bool
    {
        // Usuários só podem editar suas próprias configurações
        if (Auth::user()?->can('manage_all_settings')) {
            return true;
        }

        return $record->user_id === Auth::id();
    }

    public static function canDelete($record): bool
    {
        // Usuários só podem deletar suas próprias configurações
        if (Auth::user()?->can('manage_all_settings')) {
            return true;
        }

        return $record->user_id === Auth::id();
    }
}
