<?php

namespace App\Filament\Widgets;

use App\Models\TimeEntry;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class TimeTrackingWidget extends StatsOverviewWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $user = Auth::user();
        $todayEntry = $user->todayTimeEntry();
        $userSettings = $user->getOrCreateSettings();

        // Se não há registro de hoje, mostrar estatísticas básicas
        if (!$todayEntry) {
            return [
                Stat::make('Status', 'Não registrado')
                    ->description('Nenhum ponto registrado hoje')
                    ->descriptionIcon('heroicon-m-clock')
                    ->color('gray'),

                Stat::make('Carga Horária', $userSettings->getDailyWorkFormatted())
                    ->description('Meta diária de trabalho')
                    ->descriptionIcon('heroicon-m-briefcase')
                    ->color('info'),

                Stat::make('Horário de Entrada', Carbon::parse($userSettings->default_start_time)->format('H:i'))
                    ->description('Horário padrão configurado')
                    ->descriptionIcon('heroicon-m-arrow-right-on-rectangle')
                    ->color('success'),
            ];
        }

        $workedMinutes = $todayEntry->getTotalWorkedMinutes();
        $overtimeMinutes = $todayEntry->getOvertimeMinutes();
        $missingMinutes = $todayEntry->getMissingMinutes();
        $estimatedExit = $todayEntry->getEstimatedExitTime();

        $stats = [];

        // Status atual
        if (!$todayEntry->entry_time) {
            $stats[] = Stat::make('Status', 'Aguardando entrada')
                ->description('Clique em "Bater Ponto - Entrada"')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning');
        } elseif (!$todayEntry->exit_time) {
            $stats[] = Stat::make('Status', 'Trabalhando')
                ->description('Entrada: ' . Carbon::parse($todayEntry->entry_time)->format('H:i'))
                ->descriptionIcon('heroicon-m-clock')
                ->color('success');
        } else {
            $stats[] = Stat::make('Status', 'Finalizado')
                ->description('Saída: ' . Carbon::parse($todayEntry->exit_time)->format('H:i'))
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success');
        }

        // Horas trabalhadas
        $stats[] = Stat::make('Horas Trabalhadas', TimeEntry::formatMinutes($workedMinutes))
            ->description('Meta: ' . $userSettings->getDailyWorkFormatted())
            ->descriptionIcon('heroicon-m-clock')
            ->color($workedMinutes >= $userSettings->getDailyWorkMinutesTotal() ? 'success' : 'warning');

        // Horas extras ou devendo
        if ($overtimeMinutes > 0) {
            $stats[] = Stat::make('Horas Extras', '+' . TimeEntry::formatMinutes($overtimeMinutes))
                ->description('Tempo adicional trabalhado')
                ->descriptionIcon('heroicon-m-plus-circle')
                ->color('success');
        } elseif ($missingMinutes > 0) {
            $stats[] = Stat::make('Horas Devendo', '-' . TimeEntry::formatMinutes($missingMinutes))
                ->description('Tempo restante para completar')
                ->descriptionIcon('heroicon-m-minus-circle')
                ->color('danger');
        } else {
            $stats[] = Stat::make('Carga Horária', 'Completa')
                ->description('Meta diária atingida')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success');
        }

        // Horário estimado de saída (apenas se ainda não saiu)
        if (!$todayEntry->exit_time && $estimatedExit) {
            $stats[] = Stat::make('Saída Estimada', $estimatedExit->format('H:i'))
                ->description('Baseado na carga horária')
                ->descriptionIcon('heroicon-m-arrow-left-on-rectangle')
                ->color('info');
        }

        return $stats;
    }

    protected int | string | array $columnSpan = 'full';

    protected function getPollingInterval(): ?string
    {
        return '30s'; // Atualiza a cada 30 segundos
    }
}
