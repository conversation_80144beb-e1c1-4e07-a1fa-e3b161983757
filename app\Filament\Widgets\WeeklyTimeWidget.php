<?php

namespace App\Filament\Widgets;

use App\Models\TimeEntry;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;

class WeeklyTimeWidget extends ChartWidget
{
    protected ?string $heading = 'Horas Trabalhadas - Últimos 7 Dias';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $user = Auth::user();
        $userSettings = $user->getOrCreateSettings();
        $requiredMinutesPerDay = $userSettings->getDailyWorkMinutesTotal();

        // Últimos 7 dias
        $dates = collect();
        $workedMinutes = collect();
        $requiredMinutes = collect();

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $timeEntry = $user->timeEntries()
                ->where('date', $date->toDateString())
                ->first();

            $dates->push($date->format('d/m'));
            $workedMinutes->push($timeEntry ? round($timeEntry->getTotalWorkedMinutes() / 60, 2) : 0);
            $requiredMinutes->push(round($requiredMinutesPerDay / 60, 2));
        }

        return [
            'datasets' => [
                [
                    'label' => 'Horas Trabalhadas',
                    'data' => $workedMinutes->toArray(),
                    'backgroundColor' => 'rgba(59, 130, 246, 0.8)',
                    'borderColor' => 'rgba(59, 130, 246, 1)',
                    'borderWidth' => 1,
                ],
                [
                    'label' => 'Meta Diária',
                    'data' => $requiredMinutes->toArray(),
                    'backgroundColor' => 'rgba(34, 197, 94, 0.3)',
                    'borderColor' => 'rgba(34, 197, 94, 1)',
                    'borderWidth' => 2,
                    'type' => 'line',
                ],
            ],
            'labels' => $dates->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Horas',
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Data',
                    ],
                ],
            ],
        ];
    }

    protected function getPollingInterval(): ?string
    {
        return '5m'; // Atualiza a cada 5 minutos
    }
}
