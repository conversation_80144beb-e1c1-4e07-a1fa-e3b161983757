<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TimeEntry extends Model
{
    protected $fillable = [
        'user_id',
        'date',
        'entry_time',
        'lunch_start_time',
        'lunch_end_time',
        'exit_time',
        'notes',
        'is_manual_entry',
    ];

    protected $casts = [
        'date' => 'date',
        'entry_time' => 'datetime:H:i',
        'lunch_start_time' => 'datetime:H:i',
        'lunch_end_time' => 'datetime:H:i',
        'exit_time' => 'datetime:H:i',
        'is_manual_entry' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calcula o total de minutos trabalhados no dia
     */
    public function getTotalWorkedMinutes(): int
    {
        if (!$this->entry_time) {
            return 0;
        }

        $entryTime = Carbon::parse($this->entry_time);
        $exitTime = $this->exit_time ? Carbon::parse($this->exit_time) : Carbon::now();

        $totalMinutes = $exitTime->diffInMinutes($entryTime);

        // Subtrai o tempo de almoço se ambos os horários estiverem definidos
        if ($this->lunch_start_time && $this->lunch_end_time) {
            $lunchStart = Carbon::parse($this->lunch_start_time);
            $lunchEnd = Carbon::parse($this->lunch_end_time);
            $lunchMinutes = $lunchEnd->diffInMinutes($lunchStart);
            $totalMinutes -= $lunchMinutes;
        }

        return max(0, $totalMinutes);
    }

    /**
     * Calcula horas extras baseado na configuração do usuário
     */
    public function getOvertimeMinutes(): int
    {
        $userSettings = $this->user->settings;
        if (!$userSettings) {
            return 0;
        }

        $workedMinutes = $this->getTotalWorkedMinutes();
        $requiredMinutes = $userSettings->getDailyWorkMinutesTotal();

        return max(0, $workedMinutes - $requiredMinutes);
    }

    /**
     * Calcula minutos em falta baseado na configuração do usuário
     */
    public function getMissingMinutes(): int
    {
        $userSettings = $this->user->settings;
        if (!$userSettings) {
            return 0;
        }

        $workedMinutes = $this->getTotalWorkedMinutes();
        $requiredMinutes = $userSettings->getDailyWorkMinutesTotal();

        return max(0, $requiredMinutes - $workedMinutes);
    }

    /**
     * Calcula o horário estimado de saída baseado na carga horária
     */
    public function getEstimatedExitTime(): ?Carbon
    {
        if (!$this->entry_time) {
            return null;
        }

        $userSettings = $this->user->settings;
        if (!$userSettings) {
            return null;
        }

        $entryTime = Carbon::parse($this->entry_time);
        $requiredMinutes = $userSettings->getDailyWorkMinutesTotal();

        $estimatedExit = $entryTime->copy()->addMinutes($requiredMinutes);

        // Adiciona tempo de almoço se configurado
        if ($userSettings->auto_lunch_break) {
            $lunchStart = Carbon::parse($userSettings->lunch_start_time);
            $lunchEnd = Carbon::parse($userSettings->lunch_end_time);
            $lunchDuration = $lunchEnd->diffInMinutes($lunchStart);
            $estimatedExit->addMinutes($lunchDuration);
        }

        return $estimatedExit;
    }

    /**
     * Formata minutos em formato "Xh Ymin"
     */
    public static function formatMinutes(int $minutes): string
    {
        $hours = intval($minutes / 60);
        $mins = $minutes % 60;

        if ($hours > 0 && $mins > 0) {
            return "{$hours}h {$mins}min";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$mins}min";
        }
    }
}
