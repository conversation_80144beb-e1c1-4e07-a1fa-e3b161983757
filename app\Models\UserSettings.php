<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserSettings extends Model
{
    protected $fillable = [
        'user_id',
        'daily_work_hours',
        'daily_work_minutes',
        'default_start_time',
        'lunch_start_time',
        'lunch_end_time',
        'auto_lunch_break',
    ];

    protected $casts = [
        'default_start_time' => 'datetime:H:i',
        'lunch_start_time' => 'datetime:H:i',
        'lunch_end_time' => 'datetime:H:i',
        'auto_lunch_break' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Retorna a carga horária diária total em minutos
     */
    public function getDailyWorkMinutesTotal(): int
    {
        return ($this->daily_work_hours * 60) + $this->daily_work_minutes;
    }

    /**
     * Retorna a carga horária diária formatada (ex: "8h 30min")
     */
    public function getDailyWorkFormatted(): string
    {
        $hours = $this->daily_work_hours;
        $minutes = $this->daily_work_minutes;

        if ($minutes > 0) {
            return "{$hours}h {$minutes}min";
        }

        return "{$hours}h";
    }
}
