<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('daily_work_hours')->default(8); // Horas de trabalho por dia (em horas)
            $table->integer('daily_work_minutes')->default(0); // Minutos adicionais de trabalho por dia
            $table->time('default_start_time')->default('08:00:00'); // Horário padrão de entrada
            $table->time('lunch_start_time')->default('12:00:00'); // Horário padrão de início do almoço
            $table->time('lunch_end_time')->default('13:00:00'); // Hor<PERSON>rio padrão de fim do almoço
            $table->boolean('auto_lunch_break')->default(true); // Se deve considerar pausa para almoço automaticamente
            $table->timestamps();

            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_settings');
    }
};
