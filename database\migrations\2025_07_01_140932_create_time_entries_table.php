<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('time_entries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('date'); // Data do registro
            $table->time('entry_time')->nullable(); // Horário de entrada
            $table->time('lunch_start_time')->nullable(); // Horário de início do almoço
            $table->time('lunch_end_time')->nullable(); // Horário de fim do almoço
            $table->time('exit_time')->nullable(); // Horário de saída
            $table->text('notes')->nullable(); // Observações
            $table->boolean('is_manual_entry')->default(false); // Se foi entrada manual ou automática
            $table->timestamps();

            $table->unique(['user_id', 'date']); // Um registro por usuário por dia
            $table->index(['user_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('time_entries');
    }
};
