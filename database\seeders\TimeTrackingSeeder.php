<?php

namespace Database\Seeders;

use App\Models\TimeEntry;
use App\Models\User;
use App\Models\UserSettings;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TimeTrackingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar usuário de teste se não existir
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrador',
                'password' => bcrypt('password'),
            ]
        );

        // Criar configurações padrão para o usuário
        UserSettings::firstOrCreate(
            ['user_id' => $user->id],
            [
                'daily_work_hours' => 8,
                'daily_work_minutes' => 0,
                'default_start_time' => '08:00:00',
                'lunch_start_time' => '12:00:00',
                'lunch_end_time' => '13:00:00',
                'auto_lunch_break' => true,
            ]
        );

        // Criar alguns registros de exemplo dos últimos dias
        for ($i = 7; $i >= 1; $i--) {
            $date = Carbon::now()->subDays($i);

            // Pular fins de semana
            if ($date->isWeekend()) {
                continue;
            }

            $entryTime = $date->copy()->setTime(8, rand(0, 30), 0);
            $lunchStart = $date->copy()->setTime(12, rand(0, 15), 0);
            $lunchEnd = $lunchStart->copy()->addHour();
            $exitTime = $date->copy()->setTime(17, rand(0, 60), 0);

            TimeEntry::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'date' => $date->toDateString(),
                ],
                [
                    'entry_time' => $entryTime->format('H:i:s'),
                    'lunch_start_time' => $lunchStart->format('H:i:s'),
                    'lunch_end_time' => $lunchEnd->format('H:i:s'),
                    'exit_time' => $exitTime->format('H:i:s'),
                    'is_manual_entry' => false,
                ]
            );
        }

        $this->command->info('Usuário de teste criado: <EMAIL> / password');
        $this->command->info('Configurações e registros de exemplo criados com sucesso!');
    }
}
