<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'applyAction',
    'columns' => null,
    'hasReorderableColumns',
    'hasToggleableColumns',
    'headingTag' => 'h3',
    'reorderAnimationDuration' => 300,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'applyAction',
    'columns' => null,
    'hasReorderableColumns',
    'hasToggleableColumns',
    'headingTag' => 'h3',
    'reorderAnimationDuration' => 300,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    use Filament\Support\Enums\GridDirection;
    use Filament\Support\Facades\FilamentView;
    use Illuminate\View\ComponentAttributeBag;
?>

<div class="fi-ta-col-manager">
    <div
        x-data="filamentTableColumnManager({
                    columns: $wire.entangle('tableColumns'),
                    isLive: <?php echo e($applyAction->isVisible() ? 'false' : 'true'); ?>,
                })"
        class="fi-ta-col-manager-ctn"
    >
        <div class="fi-ta-col-manager-header">
            <<?php echo e($headingTag); ?> class="fi-ta-col-manager-heading">
                <?php echo e(__('filament-tables::table.column_manager.heading')); ?>

            </<?php echo e($headingTag); ?>>
        </div>

        <div
            <?php if($hasReorderableColumns): ?>
                x-sortable
                x-on:end.stop="reorderColumns($event.target.sortable.toArray())"
                data-sortable-animation-duration="<?php echo e($reorderAnimationDuration); ?>"
            <?php endif; ?>
            <?php echo e((new ComponentAttributeBag)
                    ->grid($columns, GridDirection::Column)
                    ->class(['fi-ta-col-manager-items'])); ?>

        >
            <template
                x-for="(column, index) in columns"
                x-bind:key="(column.type === 'group' ? 'group::' : 'column::') + column.name + '_' + index"
            >
                <div
                    <?php if($hasReorderableColumns): ?>
                        x-bind:x-sortable-item="column.type === 'group' ? 'group::' + column.name : 'column::' + column.name"
                    <?php endif; ?>
                >
                    <template x-if="column.type === 'group'">
                        <div class="fi-ta-col-manager-group">
                            <div class="fi-ta-col-manager-item">
                                <label class="fi-ta-col-manager-label">
                                    <!--[if BLOCK]><![endif]--><?php if($hasToggleableColumns): ?>
                                        <input
                                            type="checkbox"
                                            class="fi-checkbox-input fi-valid"
                                            x-bind:id="'group-' + column.name"
                                            x-bind:checked="(groupedColumns[column.name] || {}).checked || false"
                                            x-bind:disabled="(groupedColumns[column.name] || {}).disabled || false"
                                            x-effect="$el.indeterminate = (groupedColumns[column.name] || {}).indeterminate || false"
                                            x-on:change="toggleGroup(column.name)"
                                        />
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <span x-text="column.label"></span>
                                </label>

                                <!--[if BLOCK]><![endif]--><?php if($hasReorderableColumns): ?>
                                    <button
                                        x-sortable-handle
                                        x-on:click.stop
                                        class="fi-ta-col-manager-reorder-handle fi-icon-btn"
                                        type="button"
                                    >
                                        <?php echo e(\Filament\Support\generate_icon_html(\Filament\Support\Icons\Heroicon::Bars2, alias: 'tables::columns.reorder.handle')); ?>

                                    </button>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <div
                                <?php if($hasReorderableColumns): ?>
                                    x-sortable
                                    x-on:end.stop="reorderGroupColumns($event.target.sortable.toArray(), column.name)"
                                    data-sortable-animation-duration="<?php echo e($reorderAnimationDuration); ?>"
                                <?php endif; ?>
                                class="fi-ta-col-manager-group-items"
                            >
                                <template
                                    x-for="(groupColumn, index) in column.columns"
                                    x-bind:key="'column::' + groupColumn.name + '_' + index"
                                >
                                    <div
                                        <?php if($hasReorderableColumns): ?>
                                            x-bind:x-sortable-item="'column::' + groupColumn.name"
                                        <?php endif; ?>
                                    >
                                        <div class="fi-ta-col-manager-item">
                                            <label
                                                class="fi-ta-col-manager-label"
                                            >
                                                <!--[if BLOCK]><![endif]--><?php if($hasToggleableColumns): ?>
                                                    <input
                                                        type="checkbox"
                                                        class="fi-checkbox-input fi-valid"
                                                        x-bind:id="'column-' + groupColumn.name.replace('.', '-')"
                                                        x-bind:checked="(getColumn(groupColumn.name, column.name) || {}).isToggled || false"
                                                        x-bind:disabled="(getColumn(groupColumn.name, column.name) || {}).isToggleable === false"
                                                        x-on:change="toggleColumn(groupColumn.name, column.name)"
                                                    />
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                                <span
                                                    x-text="groupColumn.label"
                                                ></span>
                                            </label>

                                            <!--[if BLOCK]><![endif]--><?php if($hasReorderableColumns): ?>
                                                <button
                                                    x-sortable-handle
                                                    x-on:click.stop
                                                    class="fi-ta-col-manager-reorder-handle fi-icon-btn"
                                                    type="button"
                                                >
                                                    <?php echo e(\Filament\Support\generate_icon_html(\Filament\Support\Icons\Heroicon::Bars2, alias: 'tables::columns.reorder.handle')); ?>

                                                </button>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                    <template x-if="column.type !== 'group'">
                        <div class="fi-ta-col-manager-item">
                            <label class="fi-ta-col-manager-label">
                                <!--[if BLOCK]><![endif]--><?php if($hasToggleableColumns): ?>
                                    <input
                                        type="checkbox"
                                        class="fi-checkbox-input fi-valid"
                                        x-bind:id="'column-' + column.name.replace('.', '-')"
                                        x-bind:checked="(getColumn(column.name, null) || {}).isToggled || false"
                                        x-bind:disabled="(getColumn(column.name, null) || {}).isToggleable === false"
                                        x-on:change="toggleColumn(column.name)"
                                    />
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                <span x-text="column.label"></span>
                            </label>

                            <!--[if BLOCK]><![endif]--><?php if($hasReorderableColumns): ?>
                                <button
                                    x-sortable-handle
                                    x-on:click.stop
                                    class="fi-ta-col-manager-reorder-handle fi-icon-btn"
                                    type="button"
                                >
                                    <?php echo e(\Filament\Support\generate_icon_html(\Filament\Support\Icons\Heroicon::Bars2, alias: 'tables::columns.reorder.handle')); ?>

                                </button>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </template>
                </div>
            </template>
        </div>

        <!--[if BLOCK]><![endif]--><?php if($applyAction->isVisible()): ?>
            <div class="fi-ta-col-manager-apply-action-ctn">
                <?php echo e($applyAction); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Projetos\utilitarios-intra\vendor\filament\tables\src\/../resources/views/components/column-manager.blade.php ENDPATH**/ ?>